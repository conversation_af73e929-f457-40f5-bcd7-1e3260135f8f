<template>
  <view class="empty-state">
    <image 
      :src="imageUrl" 
      class="empty-image" 
      mode="aspectFit"
    />
    <text class="empty-title">{{ title }}</text>
    <text v-if="description" class="empty-description">{{ description }}</text>
    <u-button 
      v-if="showButton" 
      :text="buttonText" 
      type="primary"
      size="normal"
      @click="handleButtonClick"
      class="empty-button"
    />
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { EMPTY_IMAGES } from '@/constants'

interface Props {
  type?: 'no-data' | 'no-network' | 'no-permission' | 'custom'
  title?: string
  description?: string
  image?: string
  showButton?: boolean
  buttonText?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'no-data',
  title: '',
  description: '',
  image: '',
  showButton: false,
  buttonText: '重试',
})

const emit = defineEmits<{
  buttonClick: []
}>()

const imageUrl = computed(() => {
  if (props.image) return props.image
  
  switch (props.type) {
    case 'no-data':
      return EMPTY_IMAGES.NO_DATA
    case 'no-network':
      return EMPTY_IMAGES.NO_NETWORK
    case 'no-permission':
      return EMPTY_IMAGES.NO_PERMISSION
    default:
      return EMPTY_IMAGES.NO_DATA
  }
})

const title = computed(() => {
  if (props.title) return props.title
  
  switch (props.type) {
    case 'no-data':
      return '暂无数据'
    case 'no-network':
      return '网络连接失败'
    case 'no-permission':
      return '暂无权限'
    default:
      return '暂无数据'
  }
})

const handleButtonClick = () => {
  emit('buttonClick')
}
</script>

<style lang="scss" scoped>
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #8A8A8A;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-description {
  font-size: 28rpx;
  color: #BFBFBF;
  line-height: 1.5;
  margin-bottom: 48rpx;
  max-width: 400rpx;
}

.empty-button {
  margin-top: 32rpx;
}
</style>
