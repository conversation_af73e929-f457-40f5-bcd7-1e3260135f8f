/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */

// 引入uview-plus样式
@import '@/uni_modules/uview-plus/index.scss';

/* 主题色彩变量 */

/* 行为相关颜色 - 疾控主题 */
$uni-color-primary: #2E8B57; // 海绿色 - 主色调
$uni-color-success: #52C41A; // 成功色
$uni-color-warning: #FAAD14; // 警告色
$uni-color-error: #F5222D; // 错误色
$uni-color-info: #1890FF; // 信息色

/* 主题色彩扩展 */
$acdc-primary: #2E8B57; // 主色调
$acdc-primary-light: #3CB371; // 浅主色
$acdc-primary-dark: #228B22; // 深主色

/* 文字基本颜色 */
$uni-text-color: #262626; // 主要文字色
$uni-text-color-inverse: #fff; // 反色
$uni-text-color-grey: #8A8A8A; // 辅助灰色
$uni-text-color-placeholder: #BFBFBF; // 占位符颜色
$uni-text-color-disable: #D9D9D9; // 禁用文字色

/* 文字颜色扩展 */
$acdc-text-primary: #262626; // 主要文字
$acdc-text-secondary: #595959; // 次要文字
$acdc-text-disabled: #BFBFBF; // 禁用文字

/* 背景颜色 */
$uni-bg-color: #FFFFFF; // 主背景色
$uni-bg-color-grey: #F8F9FA; // 灰色背景
$uni-bg-color-hover: #F0F0F0; // 点击状态颜色
$uni-bg-color-mask: rgba(0, 0, 0, 0.4); // 遮罩颜色

/* 背景颜色扩展 */
$acdc-bg-primary: #F8F9FA; // 主背景
$acdc-bg-light: #FFFFFF; // 浅背景

/* 边框颜色 */
$uni-border-color: #E8E8E8; // 边框色
$acdc-border-color: #E8E8E8; // 边框色
$acdc-divider-color: #F0F0F0; // 分割线色

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm:12px;
$uni-font-size-base:14px;
$uni-font-size-lg:16;

/* 图片尺寸 */
$uni-img-size-sm:20px;
$uni-img-size-base:26px;
$uni-img-size-lg:40px;

/* Border Radius */
$uni-border-radius-sm: 2px;
$uni-border-radius-base: 3px;
$uni-border-radius-lg: 6px;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 5px;
$uni-spacing-row-base: 10px;
$uni-spacing-row-lg: 15px;

/* 垂直间距 */
$uni-spacing-col-sm: 4px;
$uni-spacing-col-base: 8px;
$uni-spacing-col-lg: 12px;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2C405A; // 文章标题颜色
$uni-font-size-title:20px;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle:26px;
$uni-color-paragraph: #3F536E; // 文章段落颜色
$uni-font-size-paragraph:15px;
