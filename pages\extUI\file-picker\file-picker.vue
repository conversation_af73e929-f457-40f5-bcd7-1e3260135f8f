<template>
	<view class="container">
		<uni-card :is-shadow="false" is-full>
			<text class="uni-h6">文件选择上传组件，可以选择图片、视频等任意文件并上传到当前绑定的服务空间。</text>
		</uni-card>
		<uni-section title="只选择图片" type="line">
			<view class="example-body">
				<uni-file-picker limit="9" title="最多选择9张图片" :source-type="sourceType"></uni-file-picker>
			</view>
		</uni-section>
		<uni-section title="只选择视频" type="line">
			<view class="example-body">
				<uni-file-picker limit="9" file-mediatype="video" title="最多选择9个视频" :source-type="sourceType"></uni-file-picker>
			</view>
		</uni-section>
		<uni-section title="自定义图片和视频选择的来源" type="line">
			<view class="example-body">
				<uni-file-picker limit="9" title="从相册选图" :source-type="['album']"></uni-file-picker>
			</view>
			<view class="example-body">
				<uni-file-picker limit="9" title="使用相机" file-mediatype="video" :source-type="['camera']"></uni-file-picker>
			</view>
		</uni-section>
		<!-- #ifdef H5 || MP-WEIXIN -->
		<uni-section title="选择任意文件" type="line">
			<view class="example-body">
				<uni-file-picker limit="5" file-mediatype="all" title="最多选择5个文件"></uni-file-picker>
			</view>
		</uni-section>
		<!-- #endif -->

		<uni-section title="自定义图片大小" type="line">
			<view class="example-body custom-image-box">
				<text class="text">选择头像</text>
				<uni-file-picker limit="1" :del-icon="false" disable-preview :imageStyles="imageStyles"
					file-mediatype="image">选择</uni-file-picker>
			</view>
		</uni-section>

		<uni-section title="自定义图片大小" type="line">
			<view class="example-body ">
				<uni-file-picker readonly v-model="fileLists" :imageStyles="imageStyles" file-mediatype="image">
				</uni-file-picker>
				<uni-file-picker readonly v-model="fileLists" :listStyles="listStyles" file-mediatype="all">
				</uni-file-picker>
			</view>
		</uni-section>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				sourceType: ['album', 'camera'],
				imageStyles: {
					width: 64,
					height: 64,
					border: {
						radius: '50%'
					}
				},
				listStyles: {
					// 是否显示边框
					border: true,
					// 是否显示分隔线
					dividline: true,
					// 线条样式
					borderStyle: {
						width: 1,
						color: 'blue',
						style: 'dashed',
						radius: 2
					}
				},
				fileLists: [{
					url: 'https://web-assets.dcloud.net.cn/unidoc/zh/shuijiao-small.jpg',
					extname: 'png',
					name: 'shuijiao.png'
				}, {
					url: 'https://web-assets.dcloud.net.cn/unidoc/zh/shuijiao-small.jpg',
					extname: 'png',
					name: 'uniapp-logo.png'
				}, {
					url: 'https://web-assets.dcloud.net.cn/unidoc/zh/shuijiao-small.jpg',
					extname: 'png',
					name: 'shuijiao.png'
				}]
			}
		},
		methods: {

		}
	}
</script>

<style lang="scss">
	.example-body {
		padding: 10px;
		padding-top: 0;
	}

	.custom-image-box {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
	}

	.text {
		font-size: 14px;
		color: #333;
	}
</style>
