import type { ApiResponse } from '@/types'
import { API_CONFIG, ERROR_CODES, ERROR_MESSAGES, STORAGE_KEYS } from '@/constants'

// 请求配置接口
export interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  header?: Record<string, string>
  timeout?: number
  showLoading?: boolean
  loadingText?: string
  showError?: boolean
}

// 响应拦截器类型
export interface ResponseInterceptor {
  onSuccess?: (response: any) => any
  onError?: (error: any) => any
}

class Request {
  private baseURL: string
  private timeout: number
  private defaultHeaders: Record<string, string>
  private responseInterceptors: ResponseInterceptor[] = []

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL
    this.timeout = API_CONFIG.TIMEOUT
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    }
  }

  // 添加响应拦截器
  addResponseInterceptor(interceptor: ResponseInterceptor) {
    this.responseInterceptors.push(interceptor)
  }

  // 获取请求头
  private getHeaders(customHeaders?: Record<string, string>): Record<string, string> {
    const headers = { ...this.defaultHeaders, ...customHeaders }
    
    // 添加token
    const token = uni.getStorageSync(STORAGE_KEYS.TOKEN)
    if (token) {
      headers.Authorization = `Bearer ${token}`
    }
    
    return headers
  }

  // 处理响应
  private async handleResponse(response: any): Promise<any> {
    // 执行响应拦截器
    let processedResponse = response
    for (const interceptor of this.responseInterceptors) {
      if (interceptor.onSuccess) {
        processedResponse = await interceptor.onSuccess(processedResponse)
      }
    }

    const { statusCode, data } = processedResponse

    // HTTP状态码检查
    if (statusCode >= 200 && statusCode < 300) {
      // 业务状态码检查
      if (data.code === 0 || data.code === 200) {
        return data
      } else {
        throw new Error(data.message || '请求失败')
      }
    } else {
      throw new Error(this.getErrorMessage(statusCode))
    }
  }

  // 处理错误
  private async handleError(error: any): Promise<never> {
    // 执行错误拦截器
    for (const interceptor of this.responseInterceptors) {
      if (interceptor.onError) {
        await interceptor.onError(error)
      }
    }

    throw error
  }

  // 获取错误信息
  private getErrorMessage(statusCode: number): string {
    return ERROR_MESSAGES[statusCode] || `请求失败 (${statusCode})`
  }

  // 显示加载提示
  private showLoading(text: string = '加载中...') {
    uni.showLoading({
      title: text,
      mask: true,
    })
  }

  // 隐藏加载提示
  private hideLoading() {
    uni.hideLoading()
  }

  // 显示错误提示
  private showError(message: string) {
    uni.showToast({
      title: message,
      icon: 'none',
      duration: 2000,
    })
  }

  // 通用请求方法
  async request<T = any>(config: RequestConfig): Promise<ApiResponse<T>> {
    const {
      url,
      method = 'GET',
      data,
      header,
      timeout = this.timeout,
      showLoading = false,
      loadingText = '加载中...',
      showError = true,
    } = config

    // 显示加载提示
    if (showLoading) {
      this.showLoading(loadingText)
    }

    try {
      const response = await uni.request({
        url: this.baseURL + url,
        method,
        data,
        header: this.getHeaders(header),
        timeout,
      })

      const result = await this.handleResponse(response)
      return result
    } catch (error: any) {
      if (showError) {
        this.showError(error.message || '网络请求失败')
      }
      return this.handleError(error)
    } finally {
      if (showLoading) {
        this.hideLoading()
      }
    }
  }

  // GET请求
  get<T = any>(url: string, params?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    let fullUrl = url
    if (params) {
      const query = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&')
      fullUrl += `?${query}`
    }

    return this.request<T>({
      url: fullUrl,
      method: 'GET',
      ...config,
    })
  }

  // POST请求
  post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'POST',
      data,
      ...config,
    })
  }

  // PUT请求
  put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'PUT',
      data,
      ...config,
    })
  }

  // DELETE请求
  delete<T = any>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({
      url,
      method: 'DELETE',
      ...config,
    })
  }

  // 文件上传
  async upload(config: {
    url: string
    filePath: string
    name: string
    formData?: Record<string, any>
    showLoading?: boolean
    loadingText?: string
  }): Promise<any> {
    const {
      url,
      filePath,
      name,
      formData,
      showLoading = true,
      loadingText = '上传中...',
    } = config

    if (showLoading) {
      this.showLoading(loadingText)
    }

    try {
      const response = await uni.uploadFile({
        url: this.baseURL + url,
        filePath,
        name,
        formData,
        header: this.getHeaders(),
      })

      const result = JSON.parse(response.data)
      if (result.code === 0 || result.code === 200) {
        return result
      } else {
        throw new Error(result.message || '上传失败')
      }
    } catch (error: any) {
      this.showError(error.message || '上传失败')
      throw error
    } finally {
      if (showLoading) {
        this.hideLoading()
      }
    }
  }
}

// 创建请求实例
const request = new Request()

// 添加默认响应拦截器
request.addResponseInterceptor({
  onError: async (error) => {
    // 处理token过期
    if (error.message?.includes('401') || error.message?.includes('token')) {
      // 清除token并跳转到登录页
      uni.removeStorageSync(STORAGE_KEYS.TOKEN)
      uni.removeStorageSync(STORAGE_KEYS.USER_INFO)
      
      uni.reLaunch({
        url: '/pages/login/login',
      })
    }
  },
})

export default request
