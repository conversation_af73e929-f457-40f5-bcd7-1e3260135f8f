<template>
  <view class="profile-submit-container">
    <!-- 自定义导航栏 -->
    <u-navbar 
      title="完善个人资料" 
      :autoBack="true"
      :background="{ background: 'linear-gradient(135deg, #2E8B57 0%, #228B22 100%)' }"
      titleStyle="color: #fff; font-weight: bold;"
    />
    
    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 进度指示 -->
      <view class="progress-section">
        <view class="progress-item active">
          <view class="progress-icon">
            <u-icon name="checkmark" color="#fff" size="24" />
          </view>
          <text class="progress-text">微信登录</text>
        </view>
        <view class="progress-line"></view>
        <view class="progress-item current">
          <view class="progress-icon">2</view>
          <text class="progress-text">完善资料</text>
        </view>
        <view class="progress-line"></view>
        <view class="progress-item">
          <view class="progress-icon">3</view>
          <text class="progress-text">等待审核</text>
        </view>
      </view>
      
      <!-- 表单区域 -->
      <view class="form-container">
        <u-form :model="formData" ref="formRef" :rules="rules" labelWidth="140">
          <!-- 基本信息 -->
          <view class="form-section">
            <view class="section-title">
              <u-icon name="account" color="#2E8B57" size="32" />
              <text>基本信息</text>
            </view>
            
            <u-form-item label="姓名" prop="realName" required>
              <u-input 
                v-model="formData.realName" 
                placeholder="请输入真实姓名"
                :clearable="true"
              />
            </u-form-item>
            
            <u-form-item label="手机号码" prop="phone" required>
              <u-input 
                v-model="formData.phone" 
                placeholder="请输入手机号码"
                type="number"
                :clearable="true"
              />
            </u-form-item>
            
            <u-form-item label="身份证号" prop="idCard" required>
              <u-input 
                v-model="formData.idCard" 
                placeholder="请输入身份证号码"
                :clearable="true"
              />
            </u-form-item>
          </view>
          
          <!-- 工作信息 -->
          <view class="form-section">
            <view class="section-title">
              <u-icon name="home" color="#2E8B57" size="32" />
              <text>工作信息</text>
            </view>
            
            <u-form-item label="隶属机构" prop="organization" required>
              <u-input 
                v-model="formData.organization" 
                placeholder="请选择隶属机构"
                :disabled="true"
                @click="showOrganizationPicker = true"
              >
                <template #suffix>
                  <u-icon name="arrow-right" color="#c0c4cc" />
                </template>
              </u-input>
            </u-form-item>
            
            <u-form-item label="职位" prop="position" required>
              <u-input 
                v-model="formData.position" 
                placeholder="请选择职位"
                :disabled="true"
                @click="showPositionPicker = true"
              >
                <template #suffix>
                  <u-icon name="arrow-right" color="#c0c4cc" />
                </template>
              </u-input>
            </u-form-item>
          </view>
          
          <!-- 身份验证 -->
          <view class="form-section">
            <view class="section-title">
              <u-icon name="camera" color="#2E8B57" size="32" />
              <text>身份验证</text>
            </view>
            
            <view class="photo-upload-section">
              <view class="upload-tips">
                <u-icon name="info-circle" color="#FF9500" size="28" />
                <text class="tips-text">请上传本人近期正面免冠证件照或清晰的生活照，确保五官清晰可见，用于线上考试人脸识别验证</text>
              </view>
              
              <view class="upload-area" @click="chooseImage">
                <view v-if="!formData.photo" class="upload-placeholder">
                  <u-icon name="camera-fill" color="#ccc" size="80" />
                  <text class="upload-text">点击上传本人照片</text>
                  <text class="upload-size">支持JPG、PNG格式，不超过200KB</text>
                </view>
                <image 
                  v-else 
                  :src="formData.photo" 
                  class="uploaded-image" 
                  mode="aspectFill"
                />
                <view v-if="formData.photo" class="image-actions">
                  <view class="action-btn" @click.stop="chooseImage">
                    <u-icon name="camera" color="#fff" size="24" />
                  </view>
                  <view class="action-btn" @click.stop="deleteImage">
                    <u-icon name="trash" color="#fff" size="24" />
                  </view>
                </view>
              </view>
            </view>
          </view>
        </u-form>
        
        <!-- 操作按钮 -->
        <view class="action-buttons">
          <u-button 
            class="skip-btn"
            type="info"
            plain
            @click="skipSubmit"
          >
            跳过，先去学习
          </u-button>
          
          <u-button 
            class="submit-btn"
            type="primary"
            :loading="isSubmitting"
            loadingText="提交中..."
            @click="handleSubmit"
          >
            提交审核
          </u-button>
        </view>
      </view>
    </view>
    
    <!-- 机构选择器 -->
    <u-picker
      v-model="showOrganizationPicker"
      mode="selector"
      :range="organizationList"
      @confirm="onOrganizationConfirm"
      @cancel="showOrganizationPicker = false"
    />
    
    <!-- 职位选择器 -->
    <u-picker
      v-model="showPositionPicker"
      mode="selector"
      :range="positionList"
      @confirm="onPositionConfirm"
      @cancel="showPositionPicker = false"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import api from '@/api'
import { PAGE_PATHS, UPLOAD_CONFIG, REGEX } from '@/constants'
import { validatePhone, validateIdCard } from '@/utils'

// Store
const userStore = useUserStore()
const appStore = useAppStore()

// 响应式数据
const isSubmitting = ref(false)
const showOrganizationPicker = ref(false)
const showPositionPicker = ref(false)

// 表单数据
const formData = reactive({
  realName: '',
  phone: '',
  idCard: '',
  organization: '',
  position: '',
  photo: ''
})

// 表单验证规则
const rules = {
  realName: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 10, message: '姓名长度应为2-10个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { 
      validator: (rule: any, value: string) => validatePhone(value), 
      message: '请输入正确的手机号码', 
      trigger: 'blur' 
    }
  ],
  idCard: [
    { required: true, message: '请输入身份证号码', trigger: 'blur' },
    { 
      validator: (rule: any, value: string) => validateIdCard(value), 
      message: '请输入正确的身份证号码', 
      trigger: 'blur' 
    }
  ],
  organization: [
    { required: true, message: '请选择隶属机构', trigger: 'change' }
  ],
  position: [
    { required: true, message: '请选择职位', trigger: 'change' }
  ]
}

// 机构列表
const organizationList = [
  '市疾病预防控制中心',
  '区疾病预防控制中心',
  '县疾病预防控制中心',
  '社区卫生服务中心',
  '乡镇卫生院',
  '其他医疗机构'
]

// 职位列表  
const positionList = [
  '疾控科医师',
  '预防保健科医师',
  '接种门诊医师',
  '产科医师',
  '犬伤门诊医师',
  '护士',
  '公共卫生医师',
  '其他'
]

const formRef = ref()

// 机构选择确认
const onOrganizationConfirm = (e: any) => {
  formData.organization = organizationList[e.value[0]]
  showOrganizationPicker.value = false
}

// 职位选择确认
const onPositionConfirm = (e: any) => {
  formData.position = positionList[e.value[0]]
  showPositionPicker.value = false
}

// 选择图片
const chooseImage = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      const tempFilePath = res.tempFilePaths[0]
      
      // 检查文件大小
      uni.getFileInfo({
        filePath: tempFilePath,
        success: (fileInfo) => {
          if (fileInfo.size > UPLOAD_CONFIG.MAX_SIZE) {
            appStore.showToast('图片大小不能超过200KB')
            return
          }
          
          formData.photo = tempFilePath
          uploadImage(tempFilePath)
        }
      })
    }
  })
}

// 删除图片
const deleteImage = () => {
  appStore.showModal({
    title: '确认删除',
    content: '确定要删除这张照片吗？'
  }).then((confirmed) => {
    if (confirmed) {
      formData.photo = ''
    }
  })
}

// 上传图片到服务器
const uploadImage = async (filePath: string) => {
  try {
    appStore.showLoading('上传中...')
    
    const response = await api.user.uploadPhoto(filePath)
    formData.photo = response.data.url
    
    appStore.hideLoading()
    appStore.showToast('照片上传成功', 'success')
  } catch (error: any) {
    appStore.hideLoading()
    appStore.showToast(error.message || '照片上传失败，请重试')
    formData.photo = ''
  }
}

// 跳过提交
const skipSubmit = () => {
  appStore.showModal({
    title: '确认跳过',
    content: '跳过资料提交后，您只能使用部分功能。建议完善资料后使用完整功能。',
    confirmText: '确认跳过',
    cancelText: '继续完善'
  }).then((confirmed) => {
    if (confirmed) {
      // 跳转到学习中心
      appStore.switchTab(PAGE_PATHS.STUDY)
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate()
    if (!valid) return
    
    // 检查照片是否上传
    if (!formData.photo) {
      appStore.showToast('请上传本人照片')
      return
    }
    
    isSubmitting.value = true
    
    // 提交到服务器
    const response = await api.user.submitProfile(formData)
    
    // 更新用户信息
    userStore.updateProfile(response.data)
    
    appStore.showToast('资料提交成功', 'success')
    
    // 跳转到个人中心
    setTimeout(() => {
      appStore.switchTab(PAGE_PATHS.PERSONAL)
    }, 1500)
  } catch (error: any) {
    console.error('提交失败:', error)
    appStore.showToast(error.message || '提交失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}

// 页面加载时初始化用户信息
onMounted(() => {
  if (userStore.userInfo) {
    const user = userStore.userInfo
    formData.realName = user.realName || ''
    formData.phone = user.phone || ''
    formData.idCard = user.idCard || ''
    formData.organization = user.organization || ''
    formData.position = user.position || ''
    formData.photo = user.photo || ''
  }
})
</script>

<style lang="scss" scoped>
@import '@/src/styles/global.scss';

.profile-submit-container {
  min-height: 100vh;
  background: $acdc-bg-primary;
}

.main-content {
  padding: 40rpx 30rpx;
}

.progress-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 60rpx;
  padding: 40rpx 20rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  .progress-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    
    .progress-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      font-weight: bold;
      margin-bottom: 16rpx;
      background: #e9ecef;
      color: #6c757d;
    }
    
    .progress-text {
      font-size: 24rpx;
      color: #6c757d;
    }
    
    &.active {
      .progress-icon {
        background: $acdc-primary;
        color: #fff;
      }
      
      .progress-text {
        color: $acdc-primary;
        font-weight: bold;
      }
    }
    
    &.current {
      .progress-icon {
        background: $uni-color-warning;
        color: #fff;
      }
      
      .progress-text {
        color: $uni-color-warning;
        font-weight: bold;
      }
    }
  }
  
  .progress-line {
    width: 60rpx;
    height: 4rpx;
    background: #e9ecef;
    margin: 0 20rpx;
    margin-bottom: 30rpx;
  }
}

.form-container {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.form-section {
  margin-bottom: 60rpx;
  
  &:last-child {
    margin-bottom: 40rpx;
  }
  
  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 40rpx;
    padding-bottom: 20rpx;
    border-bottom: 2rpx solid #f0f0f0;
    
    text {
      margin-left: 16rpx;
      font-size: 32rpx;
      font-weight: bold;
      color: $acdc-text-primary;
    }
  }
}

.photo-upload-section {
  .upload-tips {
    display: flex;
    align-items: flex-start;
    padding: 20rpx;
    background: #fff7e6;
    border-radius: 16rpx;
    border-left: 6rpx solid $uni-color-warning;
    margin-bottom: 40rpx;
    
    .tips-text {
      flex: 1;
      font-size: 24rpx;
      color: #666;
      line-height: 1.5;
      margin-left: 12rpx;
    }
  }
  
  .upload-area {
    position: relative;
    width: 200rpx;
    height: 200rpx;
    border: 2rpx dashed #ddd;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    
    .upload-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      
      .upload-text {
        font-size: 24rpx;
        color: #999;
        margin: 16rpx 0 8rpx;
      }
      
      .upload-size {
        font-size: 20rpx;
        color: #ccc;
      }
    }
    
    .uploaded-image {
      width: 100%;
      height: 100%;
      border-radius: 14rpx;
    }
    
    .image-actions {
      position: absolute;
      bottom: 8rpx;
      right: 8rpx;
      display: flex;
      gap: 8rpx;
      
      .action-btn {
        width: 48rpx;
        height: 48rpx;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

.action-buttons {
  display: flex;
  gap: 24rpx;
  margin-top: 60rpx;
  
  .skip-btn {
    flex: 1;
  }
  
  .submit-btn {
    flex: 2;
  }
}
</style>
