<template>
  <view class="login-container">
    <!-- 背景装饰 -->
    <view class="bg-decoration">
      <view class="circle circle-1"></view>
      <view class="circle circle-2"></view>
      <view class="circle circle-3"></view>
    </view>
    
    <!-- 主要内容 -->
    <view class="main-content">
      <!-- Logo和标题 -->
      <view class="header">
        <image 
          class="logo" 
          src="/static/images/logo.png" 
          mode="aspectFill"
        />
        <text class="app-name">疾控医护考试系统</text>
        <text class="app-desc">医护人员任职资格考试平台</text>
      </view>
      
      <!-- 登录表单 -->
      <view class="login-form">
        <!-- 协议勾选 -->
        <view class="agreement-section">
          <u-checkbox 
            v-model="agreedToTerms" 
            :customStyle="{ marginRight: '12rpx' }"
            activeColor="#2E8B57"
          />
          <text class="agreement-text">
            我已阅读并同意
            <text class="link-text" @tap="showUserAgreement">《用户服务协议》</text>
            和
            <text class="link-text" @tap="showPrivacyPolicy">《隐私政策》</text>
          </text>
        </view>
        
        <!-- 登录按钮 -->
        <u-button 
          class="login-btn"
          type="primary"
          :disabled="!agreedToTerms || isLogging"
          :loading="isLogging"
          loadingText="登录中..."
          @click="handleWeChatLogin"
        >
          <u-icon name="weixin-fill" color="#fff" size="32" class="wechat-icon" />
          微信授权登录
        </u-button>
        
        <!-- 温馨提示 -->
        <view class="tips">
          <u-icon name="info-circle" color="#999" size="24" />
          <text class="tips-text">首次登录需完善个人资料，通过机构审核后可使用完整功能</text>
        </view>
      </view>
    </view>
    
    <!-- 底部版权信息 -->
    <view class="footer">
      <text class="copyright">© 2024 疾控医护考试系统</text>
    </view>
    
    <!-- 用户协议弹窗 -->
    <u-popup 
      v-model="showAgreementModal" 
      mode="center" 
      width="640rpx" 
      height="800rpx"
      :closeOnClickOverlay="true"
    >
      <view class="agreement-modal">
        <view class="modal-header">
          <text class="modal-title">{{ currentAgreementTitle }}</text>
          <u-icon name="close" size="32" @click="showAgreementModal = false" />
        </view>
        <scroll-view class="modal-content" scroll-y>
          <text class="agreement-content">{{ currentAgreementContent }}</text>
        </scroll-view>
        <view class="modal-footer">
          <u-button type="primary" @click="showAgreementModal = false">我知道了</u-button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import api from '@/api'
import { PAGE_PATHS } from '@/constants'

// Store
const userStore = useUserStore()
const appStore = useAppStore()

// 响应式数据
const agreedToTerms = ref(false)
const isLogging = ref(false)
const showAgreementModal = ref(false)
const currentAgreementTitle = ref('')
const currentAgreementContent = ref('')

// 用户协议内容
const userAgreementContent = `1. 服务条款
本服务条款是您与疾控医护考试系统之间的协议，规定了您使用本平台服务的权利和义务。

2. 用户义务
用户承诺提供真实、准确的个人信息，不得冒用他人身份或提供虚假信息。

3. 隐私保护
我们重视您的隐私保护，严格按照相关法律法规保护您的个人信息安全。

4. 服务内容
本平台提供疾控医护人员任职资格考试相关的学习、练习、考试等服务。

5. 免责声明
在法律允许的范围内，本平台对服务中断、数据丢失等情况不承担责任。`

const privacyPolicyContent = `1. 信息收集
我们会收集您在使用服务时主动提供的信息，包括但不限于姓名、手机号、身份证号等。

2. 信息使用
收集的信息将用于提供更好的服务，包括身份验证、考试管理、证书颁发等。

3. 信息保护
我们采用行业标准的安全措施保护您的信息，不会向第三方泄露您的个人信息。

4. 信息存储
您的个人信息将存储在安全的服务器中，并采用加密技术保护。

5. 权利保障
您有权查看、修改、删除您的个人信息，如有需要请联系客服。`

// 显示用户协议
const showUserAgreement = () => {
  currentAgreementTitle.value = '用户服务协议'
  currentAgreementContent.value = userAgreementContent
  showAgreementModal.value = true
}

// 显示隐私政策
const showPrivacyPolicy = () => {
  currentAgreementTitle.value = '隐私政策'
  currentAgreementContent.value = privacyPolicyContent
  showAgreementModal.value = true
}

// 微信登录处理
const handleWeChatLogin = async () => {
  if (!agreedToTerms.value) {
    appStore.showToast('请先同意用户协议')
    return
  }
  
  isLogging.value = true
  
  try {
    // 调用微信登录
    const loginResult = await uni.login()
    
    if (loginResult[1].code) {
      // 获取用户信息
      const userProfile = await uni.getUserProfile({
        desc: '用于完善用户资料'
      })
      
      // 调用后端登录接口
      const response = await api.user.wxLogin({
        code: loginResult[1].code,
        userInfo: userProfile[1].userInfo
      })
      
      // 保存用户信息
      userStore.login(response.data.token, response.data.user)
      
      // 根据用户状态跳转
      if (response.data.user.status === 'not_submitted') {
        // 新用户或未完善资料，跳转到资料提交页
        appStore.redirectTo(PAGE_PATHS.PROFILE)
      } else {
        // 已注册用户，跳转到主页
        appStore.switchTab(PAGE_PATHS.INFO)
      }
    }
  } catch (error: any) {
    console.error('登录失败:', error)
    
    if (error.errMsg && error.errMsg.includes('getUserProfile:fail')) {
      appStore.showToast('需要授权后才能登录')
    } else {
      appStore.showToast(error.message || '登录失败，请重试')
    }
  } finally {
    isLogging.value = false
  }
}

// 页面加载时检查登录状态
onMounted(() => {
  // 如果已经登录，直接跳转
  if (userStore.isLoggedIn) {
    if (userStore.isAuthenticated) {
      appStore.switchTab(PAGE_PATHS.INFO)
    } else {
      appStore.redirectTo(PAGE_PATHS.PROFILE)
    }
  }
})
</script>

<style lang="scss" scoped>
@import '@/src/styles/global.scss';

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx 60rpx;
}

.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  
  .circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    
    &.circle-1 {
      width: 200rpx;
      height: 200rpx;
      top: 10%;
      right: 10%;
      animation: float 6s ease-in-out infinite;
    }
    
    &.circle-2 {
      width: 150rpx;
      height: 150rpx;
      bottom: 20%;
      left: 15%;
      animation: float 8s ease-in-out infinite reverse;
    }
    
    &.circle-3 {
      width: 100rpx;
      height: 100rpx;
      top: 30%;
      left: 10%;
      animation: float 7s ease-in-out infinite;
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  max-width: 500rpx;
}

.header {
  text-align: center;
  margin-bottom: 100rpx;
  
  .logo {
    width: 120rpx;
    height: 120rpx;
    border-radius: 24rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  }
  
  .app-name {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: #fff;
    margin-bottom: 16rpx;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }
  
  .app-desc {
    display: block;
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
  }
}

.login-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  backdrop-filter: blur(10px);
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);
}

.agreement-section {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;
  
  .agreement-text {
    flex: 1;
    font-size: 26rpx;
    color: #666;
    line-height: 1.5;
    
    .link-text {
      color: #2E8B57;
      text-decoration: underline;
    }
  }
}

.login-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
  box-shadow: 0 8rpx 24rpx rgba(46, 139, 87, 0.3);
  margin-bottom: 40rpx;
  
  .wechat-icon {
    margin-right: 16rpx;
  }
}

.tips {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border-left: 6rpx solid #2E8B57;
  
  .tips-text {
    flex: 1;
    font-size: 24rpx;
    color: #666;
    line-height: 1.5;
    margin-left: 12rpx;
  }
}

.footer {
  margin-top: 60rpx;
  
  .copyright {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.6);
    text-align: center;
  }
}

.agreement-modal {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40rpx;
    border-bottom: 2rpx solid #f0f0f0;
    
    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .modal-content {
    flex: 1;
    padding: 40rpx;
    
    .agreement-content {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
      white-space: pre-line;
    }
  }
  
  .modal-footer {
    padding: 40rpx;
    border-top: 2rpx solid #f0f0f0;
  }
}
</style>
