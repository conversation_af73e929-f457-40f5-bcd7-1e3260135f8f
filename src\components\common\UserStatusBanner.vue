<template>
  <view v-if="shouldShowBanner" class="user-status-banner" :class="bannerClass">
    <view class="banner-content">
      <u-icon :name="statusIcon" :color="iconColor" size="32" />
      <view class="banner-text">
        <text class="status-title">{{ statusTitle }}</text>
        <text class="status-message">{{ statusMessage }}</text>
      </view>
      <u-button 
        v-if="showAction"
        :type="actionButtonType"
        size="mini"
        @click="handleAction"
        class="banner-action"
      >
        {{ actionText }}
      </u-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import { PAGE_PATHS } from '@/constants'
import type { UserStatus } from '@/types'

interface Props {
  // 是否总是显示（默认只在非正常状态时显示）
  alwaysShow?: boolean
  // 是否显示操作按钮
  showAction?: boolean
  // 自定义样式类
  customClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  alwaysShow: false,
  showAction: true,
  customClass: ''
})

const userStore = useUserStore()
const appStore = useAppStore()

// 是否应该显示横幅
const shouldShowBanner = computed(() => {
  if (!userStore.isLoggedIn) return false
  
  if (props.alwaysShow) return true
  
  // 只在非正常状态时显示
  const status = userStore.userInfo?.status
  return status !== 'approved'
})

// 横幅样式类
const bannerClass = computed(() => {
  const status = userStore.userInfo?.status
  const baseClass = 'banner'
  
  switch (status) {
    case 'not_submitted':
      return `${baseClass} banner-warning ${props.customClass}`
    case 'pending':
      return `${baseClass} banner-info ${props.customClass}`
    case 'approved':
      return `${baseClass} banner-success ${props.customClass}`
    case 'rejected':
      return `${baseClass} banner-error ${props.customClass}`
    default:
      return `${baseClass} banner-default ${props.customClass}`
  }
})

// 状态图标
const statusIcon = computed(() => {
  const status = userStore.userInfo?.status
  
  switch (status) {
    case 'not_submitted':
      return 'edit-pen'
    case 'pending':
      return 'clock'
    case 'approved':
      return 'checkmark-circle'
    case 'rejected':
      return 'close-circle'
    default:
      return 'info-circle'
  }
})

// 图标颜色
const iconColor = computed(() => {
  const status = userStore.userInfo?.status
  
  switch (status) {
    case 'not_submitted':
      return '#FAAD14'
    case 'pending':
      return '#1890FF'
    case 'approved':
      return '#52C41A'
    case 'rejected':
      return '#F5222D'
    default:
      return '#8A8A8A'
  }
})

// 状态标题
const statusTitle = computed(() => {
  const status = userStore.userInfo?.status
  
  switch (status) {
    case 'not_submitted':
      return '资料未完善'
    case 'pending':
      return '资料审核中'
    case 'approved':
      return '认证成功'
    case 'rejected':
      return '审核未通过'
    default:
      return '未知状态'
  }
})

// 状态消息
const statusMessage = computed(() => {
  const status = userStore.userInfo?.status
  
  switch (status) {
    case 'not_submitted':
      return '请完善个人资料以使用完整功能'
    case 'pending':
      return '机构正在审核您的资料，请耐心等待'
    case 'approved':
      return '您已通过机构认证，可使用所有功能'
    case 'rejected':
      return '资料审核未通过，请重新提交正确信息'
    default:
      return ''
  }
})

// 操作按钮文本
const actionText = computed(() => {
  const status = userStore.userInfo?.status
  
  switch (status) {
    case 'not_submitted':
      return '立即完善'
    case 'pending':
      return '查看详情'
    case 'approved':
      return '查看证书'
    case 'rejected':
      return '重新提交'
    default:
      return '查看详情'
  }
})

// 操作按钮类型
const actionButtonType = computed(() => {
  const status = userStore.userInfo?.status
  
  switch (status) {
    case 'not_submitted':
      return 'warning'
    case 'pending':
      return 'info'
    case 'approved':
      return 'success'
    case 'rejected':
      return 'error'
    default:
      return 'default'
  }
})

// 处理操作按钮点击
const handleAction = () => {
  const status = userStore.userInfo?.status
  
  switch (status) {
    case 'not_submitted':
      appStore.redirectTo(PAGE_PATHS.PROFILE)
      break
    case 'pending':
    case 'approved':
      appStore.switchTab(PAGE_PATHS.PERSONAL)
      break
    case 'rejected':
      appStore.redirectTo(PAGE_PATHS.PROFILE)
      break
    default:
      appStore.switchTab(PAGE_PATHS.PERSONAL)
  }
}
</script>

<style lang="scss" scoped>
.user-status-banner {
  margin: 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.banner-content {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  gap: 24rpx;
}

.banner-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.status-title {
  font-size: 28rpx;
  font-weight: bold;
  line-height: 1.2;
}

.status-message {
  font-size: 24rpx;
  line-height: 1.4;
  opacity: 0.8;
}

.banner-action {
  flex-shrink: 0;
}

/* 不同状态的样式 */
.banner-warning {
  background: linear-gradient(135deg, #FFF7E6 0%, #FFFBE6 100%);
  border: 1rpx solid #FFE58F;
  
  .status-title,
  .status-message {
    color: #D48806;
  }
}

.banner-info {
  background: linear-gradient(135deg, #E6F7FF 0%, #F0F9FF 100%);
  border: 1rpx solid #91D5FF;
  
  .status-title,
  .status-message {
    color: #0958D9;
  }
}

.banner-success {
  background: linear-gradient(135deg, #F6FFED 0%, #F9FFF6 100%);
  border: 1rpx solid #B7EB8F;
  
  .status-title,
  .status-message {
    color: #389E0D;
  }
}

.banner-error {
  background: linear-gradient(135deg, #FFF2F0 0%, #FFF7F6 100%);
  border: 1rpx solid #FFCCC7;
  
  .status-title,
  .status-message {
    color: #CF1322;
  }
}

.banner-default {
  background: linear-gradient(135deg, #FAFAFA 0%, #F5F5F5 100%);
  border: 1rpx solid #D9D9D9;
  
  .status-title,
  .status-message {
    color: #595959;
  }
}
</style>
